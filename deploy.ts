// Deno Deploy 专用入口文件
import { start } from "$fresh/server.ts";
import config from "./fresh.config.ts";

// 手动导入所有路由
import * as $_404 from "./routes/_404.tsx";
import * as $_app from "./routes/_app.tsx";
import * as $_middleware from "./routes/_middleware.ts";
import * as $api_auth_logout from "./routes/api/auth/logout.ts";
import * as $api_tasks_id_ from "./routes/api/tasks/[id].ts";
import * as $api_tasks_id_execute from "./routes/api/tasks/[id]/execute.ts";
import * as $api_tasks_id_results from "./routes/api/tasks/[id]/results.ts";
import * as $api_tasks_index from "./routes/api/tasks/index.ts";
import * as $dashboard from "./routes/dashboard.tsx";
import * as $index from "./routes/index.tsx";
import * as $login from "./routes/login.tsx";
import * as $register from "./routes/register.tsx";

import { type Manifest } from "$fresh/server.ts";

// 创建 manifest
const manifest = {
  routes: {
    "./routes/_404.tsx": $_404,
    "./routes/_app.tsx": $_app,
    "./routes/_middleware.ts": $_middleware,
    "./routes/api/auth/logout.ts": $api_auth_logout,
    "./routes/api/tasks/[id].ts": $api_tasks_id_,
    "./routes/api/tasks/[id]/execute.ts": $api_tasks_id_execute,
    "./routes/api/tasks/[id]/results.ts": $api_tasks_id_results,
    "./routes/api/tasks/index.ts": $api_tasks_index,
    "./routes/dashboard.tsx": $dashboard,
    "./routes/index.tsx": $index,
    "./routes/login.tsx": $login,
    "./routes/register.tsx": $register,
  },
  islands: {},
  baseUrl: import.meta.url,
} satisfies Manifest;

// 动态导入 KV 和调度器（避免导入错误）
async function initApp() {
  try {
    console.log("🚀 开始初始化应用...");
    
    // 动态导入 KV 模块
    const { initKV } = await import("./lib/kv.ts");
    await initKV();
    console.log("✅ KV 数据库初始化完成");

    // 动态导入调度器模块
    const { Scheduler } = await import("./lib/scheduler.ts");
    await Scheduler.init();
    console.log("✅ 监控调度器初始化完成");

    console.log("🎉 应用初始化完成");
  } catch (error) {
    console.error("❌ 应用初始化失败:", error);
    console.log("⚠️ 继续启动服务器（部分功能可能不可用）");
  }
}

// 启动应用
async function main() {
  await initApp();
  await start(manifest, config);
}

// 执行启动
await main();
