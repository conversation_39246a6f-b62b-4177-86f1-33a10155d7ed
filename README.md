# 网站监控系统

一个基于 Deno Deploy 的网站监控和测速系统，支持定时访问网站、记录响应时间、状态监控等功能。

## 功能特性

- 🕐 **定时监控**: 支持自定义监控间隔和随机范围
- 📊 **性能分析**: 实时记录响应时间、状态码和成功率
- 🔐 **用户认证**: 完整的登录注册系统
- 🍪 **Cookie 支持**: 可选择是否携带 Cookie 进行请求
- 📝 **日志系统**: 完整的操作日志和错误记录
- 🗄️ **数据存储**: 使用 Deno Deploy KV 存储数据
- 🧹 **自动清理**: 可配置日志保留天数，自动清理过期数据
- 📱 **响应式设计**: 支持桌面和移动设备

## 技术栈

- **后端**: Deno + Fresh Framework
- **前端**: TypeScript + Preact + Tailwind CSS
- **数据库**: Deno Deploy KV
- **部署**: Deno Deploy
- **认证**: JWT + bcrypt

## 项目结构

```
/
├── lib/                    # 核心库
│   ├── auth.ts            # 认证相关
│   ├── kv.ts              # KV 数据库操作
│   ├── monitor.ts         # 监控服务
│   ├── scheduler.ts       # 定时任务调度
│   └── utils.ts           # 工具函数
├── routes/                # 路由
│   ├── api/               # API 路由
│   ├── dashboard.tsx      # 仪表板
│   ├── login.tsx          # 登录页
│   ├── register.tsx       # 注册页
│   └── index.tsx          # 首页
├── types/                 # 类型定义
├── static/                # 静态资源
├── main.ts                # 应用入口
├── deno.json              # Deno 配置
└── fresh.config.ts        # Fresh 配置
```

## 快速开始

### 本地开发

1. 确保已安装 Deno (v1.40+)

2. 克隆项目并进入目录

3. 启动开发服务器:
   ```bash
   deno task start
   ```

4. 访问 http://localhost:8000

### 部署到 Deno Deploy

1. 在 [Deno Deploy](https://dash.deno.com) 创建新项目

2. 连接您的 GitHub 仓库

3. 设置环境变量:
   - `JWT_SECRET`: JWT 密钥（建议使用强随机字符串）

4. 部署完成后，系统将自动初始化 KV 数据库和定时任务

## 使用说明

### 创建监控任务

1. 注册账号并登录
2. 在控制台点击"新建任务"
3. 填写以下信息:
   - **任务名称**: 便于识别的名称
   - **监控URL**: 要监控的网站地址
   - **请求方法**: GET 或 POST
   - **监控间隔**: 执行间隔时间（分钟）
   - **随机范围**: 间隔时间的随机偏移（分钟）
   - **使用Cookie**: 是否携带 Cookie
   - **自定义请求头**: 可选的 HTTP 请求头

### 查看监控结果

- 在控制台可以查看任务列表和统计信息
- 点击任务可以查看详细的监控历史
- 支持查看响应时间趋势和成功率统计

### 系统配置

系统支持以下配置项:
- **日志保留天数**: 默认 30 天
- **用户最大任务数**: 默认 10 个
- **最小监控间隔**: 默认 1 分钟
- **最大监控间隔**: 默认 1440 分钟（24小时）

## API 文档

### 认证

所有 API 请求需要在 Header 中包含 JWT Token:
```
Authorization: Bearer <token>
```

### 主要端点

- `GET /api/tasks` - 获取用户任务列表
- `POST /api/tasks` - 创建新任务
- `GET /api/tasks/{id}` - 获取任务详情
- `PUT /api/tasks/{id}` - 更新任务
- `DELETE /api/tasks/{id}` - 删除任务
- `POST /api/tasks/{id}/execute` - 手动执行任务
- `GET /api/tasks/{id}/results` - 获取任务监控结果

## 定时任务

系统使用 Deno.cron 实现定时任务:

- **监控任务**: 每分钟检查并执行到期的监控任务
- **日志清理**: 每天凌晨2点清理过期日志

## 数据模型

### 用户 (User)
- id, username, email, passwordHash
- createdAt, lastLoginAt

### 监控任务 (MonitorTask)
- id, userId, name, url, method
- headers, body, useCookie, cookies
- intervalMinutes, randomRangeMinutes
- isActive, createdAt, updatedAt
- lastRunAt, nextRunAt

### 监控结果 (MonitorResult)
- id, taskId, url, status
- responseTime, success, error
- responseSize, timestamp

### 系统日志 (SystemLog)
- id, level, message, details
- userId, taskId, timestamp

## 环境变量

- `JWT_SECRET`: JWT 签名密钥（必需）
- `DENO_ENV`: 环境标识（可选，默认 production）

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 支持

如有问题，请在 GitHub 上创建 Issue。
