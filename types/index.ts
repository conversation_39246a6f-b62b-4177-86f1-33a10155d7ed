// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  passwordHash: string;
  createdAt: Date;
  lastLoginAt?: Date;
}

// 监控任务类型
export interface MonitorTask {
  id: string;
  userId: string;
  name: string;
  url: string;
  method: 'GET' | 'POST';
  headers?: Record<string, string>;
  body?: string;
  useCookie: boolean;
  cookies?: string;
  intervalMinutes: number;
  randomRangeMinutes: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastRunAt?: Date;
  nextRunAt?: Date;
}

// 监控结果类型
export interface MonitorResult {
  id: string;
  taskId: string;
  url: string;
  status: number;
  responseTime: number; // 毫秒
  success: boolean;
  error?: string;
  responseSize?: number;
  timestamp: Date;
}

// 系统日志类型
export interface SystemLog {
  id: string;
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  message: string;
  details?: Record<string, unknown>;
  userId?: string;
  taskId?: string;
  timestamp: Date;
}

// 系统配置类型
export interface SystemConfig {
  logRetentionDays: number;
  maxTasksPerUser: number;
  minIntervalMinutes: number;
  maxIntervalMinutes: number;
  enableRegistration: boolean;
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 登录请求类型
export interface LoginRequest {
  username: string;
  password: string;
}

// 注册请求类型
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// 创建任务请求类型
export interface CreateTaskRequest {
  name: string;
  url: string;
  method: 'GET' | 'POST';
  headers?: Record<string, string>;
  body?: string;
  useCookie: boolean;
  cookies?: string;
  intervalMinutes: number;
  randomRangeMinutes: number;
}

// 更新任务请求类型
export interface UpdateTaskRequest extends Partial<CreateTaskRequest> {
  isActive?: boolean;
}

// 仪表板统计类型
export interface DashboardStats {
  totalTasks: number;
  activeTasks: number;
  totalResults: number;
  successRate: number;
  averageResponseTime: number;
  recentResults: MonitorResult[];
}

// JWT 载荷类型
export interface JWTPayload {
  userId: string;
  username: string;
  exp: number;
}

// KV 键前缀
export const KV_KEYS = {
  USER: 'user:',
  TASK: 'task:',
  RESULT: 'result:',
  LOG: 'log:',
  CONFIG: 'config',
  USER_TASKS: 'user_tasks:',
  TASK_RESULTS: 'task_results:',
} as const;
