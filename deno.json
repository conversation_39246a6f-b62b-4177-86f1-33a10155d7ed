{"lock": false, "tasks": {"check": "deno fmt --check && deno lint && deno check **/*.ts && deno check **/*.tsx", "cli": "echo \"import '\\$fresh/src/dev/cli.ts'\" | deno run --unstable -A -", "manifest": "deno task cli manifest $(pwd)", "start": "deno run -A main.ts", "dev": "deno run -A --watch=static/,routes/ dev.ts", "build": "deno run -A dev.ts build", "preview": "deno run -A main.ts", "update": "deno run -A -r https://fresh.deno.dev/update ."}, "lint": {"rules": {"tags": ["fresh", "recommended"]}}, "exclude": ["**/_fresh/*"], "imports": {"$fresh/": "https://deno.land/x/fresh@1.6.8/", "preact": "https://esm.sh/preact@10.19.6", "preact/": "https://esm.sh/preact@10.19.6/", "preact-render-to-string": "https://esm.sh/*preact-render-to-string@6.2.2", "@preact/signals": "https://esm.sh/*@preact/signals@1.2.2", "@preact/signals-core": "https://esm.sh/*@preact/signals-core@1.5.1", "twind": "https://esm.sh/twind@0.16.19", "twind/": "https://esm.sh/twind@0.16.19/", "$std/": "https://deno.land/std@0.216.0/", "bcrypt": "https://deno.land/x/bcrypt@v0.4.1/mod.ts", "jose": "https://deno.land/x/jose@v5.2.0/index.ts"}, "compilerOptions": {"allowJs": true, "lib": ["dom", "dom.iterable", "es6"], "jsx": "react-jsx", "jsxImportSource": "preact"}}