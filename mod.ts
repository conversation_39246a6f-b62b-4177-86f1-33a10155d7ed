// Deno Deploy 入口文件
import { start } from "$fresh/server.ts";
import config from "./fresh.config.ts";
import manifest from "./fresh.gen.ts";
import { initKV } from "./lib/kv.ts";
import { Scheduler } from "./lib/scheduler.ts";

// 初始化应用
async function initApp() {
  try {
    // 初始化 KV 数据库
    await initKV();
    console.log("✅ KV 数据库初始化完成");

    // 初始化调度器
    await Scheduler.init();
    console.log("✅ 监控调度器初始化完成");

    console.log("🚀 应用初始化完成");
  } catch (error) {
    console.error("❌ 应用初始化失败:", error);
    Deno.exit(1);
  }
}

// 启动应用
async function main() {
  await initApp();
  await start(manifest, config);
}

// 启动应用
await main();
