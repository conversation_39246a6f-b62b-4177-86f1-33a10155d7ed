import { Head } from "$fresh/runtime.ts";
import { Handlers, PageProps } from "$fresh/server.ts";
import { State } from "./_middleware.ts";
import { TaskService, ResultService } from "../lib/kv.ts";
import { MonitorService } from "../lib/monitor.ts";
import { formatDateTime, formatResponseTime, getStatusColor, getSuccessRateColor } from "../lib/utils.ts";

interface DashboardData {
  tasks: any[];
  stats: {
    totalTasks: number;
    activeTasks: number;
    totalResults: number;
    successRate: number;
    averageResponseTime: number;
  };
  recentResults: any[];
}

export const handler: Handlers<DashboardData, State> = {
  async GET(req, ctx) {
    if (!ctx.state.user) {
      return new Response("", {
        status: 302,
        headers: { Location: "/login" },
      });
    }

    try {
      // 获取用户任务
      const tasks = await TaskService.getTasksByUser(ctx.state.user.id);
      
      // 为每个任务添加统计信息
      const tasksWithStats = await Promise.all(
        tasks.map(async (task) => {
          const stats = await MonitorService.getTaskStats(task.id);
          return {
            ...task,
            stats,
          };
        })
      );

      // 计算总体统计
      const totalTasks = tasks.length;
      const activeTasks = tasks.filter(t => t.isActive).length;
      
      // 获取最近的监控结果
      const recentResults = await ResultService.getRecentResults(20);
      const userResults = recentResults.filter(result => 
        tasks.some(task => task.id === result.taskId)
      );

      const totalResults = userResults.length;
      const successCount = userResults.filter(r => r.success).length;
      const successRate = totalResults > 0 ? (successCount / totalResults) * 100 : 0;
      const averageResponseTime = totalResults > 0 
        ? userResults.reduce((sum, r) => sum + r.responseTime, 0) / totalResults 
        : 0;

      const stats = {
        totalTasks,
        activeTasks,
        totalResults,
        successRate,
        averageResponseTime,
      };

      return ctx.render({
        tasks: tasksWithStats,
        stats,
        recentResults: userResults.slice(0, 10),
      });
    } catch (error) {
      console.error("获取仪表板数据失败:", error);
      return ctx.render({
        tasks: [],
        stats: {
          totalTasks: 0,
          activeTasks: 0,
          totalResults: 0,
          successRate: 0,
          averageResponseTime: 0,
        },
        recentResults: [],
      });
    }
  },
};

export default function Dashboard({ data, state }: PageProps<DashboardData, State>) {
  const { tasks, stats, recentResults } = data;
  const { user } = state;

  return (
    <>
      <Head>
        <title>控制台 - 网站监控系统</title>
      </Head>
      
      <div class="min-h-screen bg-gray-50">
        {/* 导航栏 */}
        <nav class="bg-white shadow-sm border-b">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex items-center">
                <a href="/" class="text-xl font-semibold text-gray-900">
                  网站监控系统
                </a>
              </div>
              <div class="flex items-center space-x-4">
                <span class="text-gray-700">欢迎，{user?.username}</span>
                <a
                  href="/api/auth/logout"
                  class="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  退出
                </a>
              </div>
            </div>
          </div>
        </nav>

        {/* 主要内容 */}
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {/* 页面标题 */}
          <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">监控控制台</h1>
            <p class="mt-2 text-gray-600">管理您的网站监控任务</p>
          </div>

          {/* 统计卡片 */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">总任务数</dt>
                      <dd class="text-lg font-medium text-gray-900">{stats.totalTasks}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">活跃任务</dt>
                      <dd class="text-lg font-medium text-gray-900">{stats.activeTasks}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">成功率</dt>
                      <dd class={`text-lg font-medium ${getSuccessRateColor(stats.successRate)}`}>
                        {stats.successRate.toFixed(1)}%
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                      <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">平均响应时间</dt>
                      <dd class="text-lg font-medium text-gray-900">
                        {formatResponseTime(stats.averageResponseTime)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 任务列表和最近结果 */}
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 任务列表 */}
            <div class="bg-white shadow rounded-lg">
              <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg leading-6 font-medium text-gray-900">监控任务</h3>
                  <button
                    onclick="showCreateTaskModal()"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    新建任务
                  </button>
                </div>
                
                {tasks.length === 0 ? (
                  <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">暂无监控任务</h3>
                    <p class="mt-1 text-sm text-gray-500">开始创建您的第一个监控任务</p>
                  </div>
                ) : (
                  <div class="space-y-3">
                    {tasks.map((task) => (
                      <div key={task.id} class="border rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">{task.name}</h4>
                            <p class="text-sm text-gray-500 truncate">{task.url}</p>
                            <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                              <span>间隔: {task.intervalMinutes}分钟</span>
                              <span>状态: {task.isActive ? "运行中" : "已停止"}</span>
                              {task.stats?.lastResult && (
                                <span class={getStatusColor(task.stats.lastResult.status)}>
                                  {task.stats.lastResult.status}
                                </span>
                              )}
                            </div>
                          </div>
                          <div class="flex items-center space-x-2">
                            <button
                              onclick={`executeTask('${task.id}')`}
                              class="text-blue-600 hover:text-blue-900 text-sm"
                            >
                              执行
                            </button>
                            <button
                              onclick={`editTask('${task.id}')`}
                              class="text-gray-600 hover:text-gray-900 text-sm"
                            >
                              编辑
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* 最近结果 */}
            <div class="bg-white shadow rounded-lg">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">最近监控结果</h3>
                
                {recentResults.length === 0 ? (
                  <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">暂无监控结果</h3>
                    <p class="mt-1 text-sm text-gray-500">执行监控任务后将显示结果</p>
                  </div>
                ) : (
                  <div class="space-y-3">
                    {recentResults.map((result) => (
                      <div key={result.id} class="border rounded-lg p-3">
                        <div class="flex items-center justify-between">
                          <div class="flex-1">
                            <div class="flex items-center space-x-2">
                              <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {result.success ? '成功' : '失败'}
                              </span>
                              <span class={`text-sm font-medium ${getStatusColor(result.status)}`}>
                                {result.status}
                              </span>
                            </div>
                            <p class="text-sm text-gray-500 truncate mt-1">{result.url}</p>
                          </div>
                          <div class="text-right">
                            <p class="text-sm font-medium text-gray-900">
                              {formatResponseTime(result.responseTime)}
                            </p>
                            <p class="text-xs text-gray-500">
                              {formatDateTime(new Date(result.timestamp))}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* 简单的 JavaScript 功能 */}
      <script dangerouslySetInnerHTML={{
        __html: `
          function showCreateTaskModal() {
            alert('创建任务功能需要在前端实现');
          }
          
          function editTask(taskId) {
            alert('编辑任务功能需要在前端实现: ' + taskId);
          }
          
          async function executeTask(taskId) {
            try {
              const response = await fetch('/api/tasks/' + taskId + '/execute', {
                method: 'POST'
              });
              const result = await response.json();
              if (result.success) {
                alert('任务执行成功');
                location.reload();
              } else {
                alert('任务执行失败: ' + result.error);
              }
            } catch (error) {
              alert('执行失败: ' + error.message);
            }
          }
        `
      }} />
    </>
  );
}
