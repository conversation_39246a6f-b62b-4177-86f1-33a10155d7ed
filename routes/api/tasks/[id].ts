import { Handlers } from "$fresh/server.ts";
import { State } from "../../_middleware.ts";
import { TaskService } from "../../../lib/kv.ts";
import { UpdateTaskRequest } from "../../../types/index.ts";
import { isValidUrl } from "../../../lib/utils.ts";
import { createSuccessResponse, createErrorResponse } from "../../../lib/utils.ts";
import { MonitorService } from "../../../lib/monitor.ts";

export const handler: Handlers<unknown, State> = {
  // 获取单个任务详情
  async GET(req, ctx) {
    if (!ctx.state.user) {
      return new Response(
        JSON.stringify(createErrorResponse("未授权访问")),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    const url = new URL(req.url);
    const taskId = url.pathname.split("/").pop();

    if (!taskId) {
      return new Response(
        JSON.stringify(createErrorResponse("任务ID不能为空")),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      const task = await TaskService.getById(taskId);
      
      if (!task) {
        return new Response(
          JSON.stringify(createErrorResponse("任务不存在")),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      // 检查任务所有权
      if (task.userId !== ctx.state.user.id) {
        return new Response(
          JSON.stringify(createErrorResponse("无权访问此任务")),
          { status: 403, headers: { "Content-Type": "application/json" } }
        );
      }

      // 获取任务统计信息
      const stats = await MonitorService.getTaskStats(task.id);

      return new Response(
        JSON.stringify(createSuccessResponse({ ...task, stats })),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("获取任务详情失败:", error);
      return new Response(
        JSON.stringify(createErrorResponse("获取任务详情失败")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // 更新任务
  async PUT(req, ctx) {
    if (!ctx.state.user) {
      return new Response(
        JSON.stringify(createErrorResponse("未授权访问")),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    const url = new URL(req.url);
    const taskId = url.pathname.split("/").pop();

    if (!taskId) {
      return new Response(
        JSON.stringify(createErrorResponse("任务ID不能为空")),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      const task = await TaskService.getById(taskId);
      
      if (!task) {
        return new Response(
          JSON.stringify(createErrorResponse("任务不存在")),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      // 检查任务所有权
      if (task.userId !== ctx.state.user.id) {
        return new Response(
          JSON.stringify(createErrorResponse("无权修改此任务")),
          { status: 403, headers: { "Content-Type": "application/json" } }
        );
      }

      const body: UpdateTaskRequest = await req.json();

      // 验证输入
      if (body.url && !isValidUrl(body.url)) {
        return new Response(
          JSON.stringify(createErrorResponse("URL格式不正确")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      if (body.intervalMinutes && (body.intervalMinutes < 1 || body.intervalMinutes > 1440)) {
        return new Response(
          JSON.stringify(createErrorResponse("监控间隔必须在1-1440分钟之间")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      if (body.randomRangeMinutes && (body.randomRangeMinutes < 0 || body.randomRangeMinutes > 60)) {
        return new Response(
          JSON.stringify(createErrorResponse("随机范围必须在0-60分钟之间")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      // 更新任务
      const updates: Partial<typeof task> = { ...body };
      
      // 如果更新了间隔时间，重新计算下次执行时间
      if (body.intervalMinutes || body.randomRangeMinutes) {
        const updatedTask = { ...task, ...updates };
        updates.nextRunAt = MonitorService.calculateNextRunTime(updatedTask);
      }

      await TaskService.update(taskId, updates);

      const updatedTask = await TaskService.getById(taskId);
      
      return new Response(
        JSON.stringify(createSuccessResponse(updatedTask, "任务更新成功")),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("更新任务失败:", error);
      return new Response(
        JSON.stringify(createErrorResponse("更新任务失败")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // 删除任务
  async DELETE(req, ctx) {
    if (!ctx.state.user) {
      return new Response(
        JSON.stringify(createErrorResponse("未授权访问")),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    const url = new URL(req.url);
    const taskId = url.pathname.split("/").pop();

    if (!taskId) {
      return new Response(
        JSON.stringify(createErrorResponse("任务ID不能为空")),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      const task = await TaskService.getById(taskId);
      
      if (!task) {
        return new Response(
          JSON.stringify(createErrorResponse("任务不存在")),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      // 检查任务所有权
      if (task.userId !== ctx.state.user.id) {
        return new Response(
          JSON.stringify(createErrorResponse("无权删除此任务")),
          { status: 403, headers: { "Content-Type": "application/json" } }
        );
      }

      await TaskService.delete(taskId);

      return new Response(
        JSON.stringify(createSuccessResponse(null, "任务删除成功")),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("删除任务失败:", error);
      return new Response(
        JSON.stringify(createErrorResponse("删除任务失败")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
