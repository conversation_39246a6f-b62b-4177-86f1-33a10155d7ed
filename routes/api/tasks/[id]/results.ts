import { Handlers } from "$fresh/server.ts";
import { State } from "../../../_middleware.ts";
import { TaskService, ResultService } from "../../../../lib/kv.ts";
import { createSuccessResponse, createErrorResponse } from "../../../../lib/utils.ts";

export const handler: Handlers<unknown, State> = {
  // 获取任务的监控结果
  async GET(req, ctx) {
    if (!ctx.state.user) {
      return new Response(
        JSON.stringify(createErrorResponse("未授权访问")),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const taskId = pathParts[pathParts.length - 2]; // 倒数第二个部分是 taskId

    if (!taskId) {
      return new Response(
        JSON.stringify(createErrorResponse("任务ID不能为空")),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      const task = await TaskService.getById(taskId);
      
      if (!task) {
        return new Response(
          JSON.stringify(createErrorResponse("任务不存在")),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      // 检查任务所有权
      if (task.userId !== ctx.state.user.id) {
        return new Response(
          JSON.stringify(createErrorResponse("无权访问此任务的结果")),
          { status: 403, headers: { "Content-Type": "application/json" } }
        );
      }

      // 获取查询参数
      const searchParams = url.searchParams;
      const limit = parseInt(searchParams.get("limit") || "50");
      const page = parseInt(searchParams.get("page") || "1");

      // 获取监控结果
      const results = await ResultService.getResultsByTask(taskId, limit * page);
      
      // 分页处理
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedResults = results.slice(startIndex, endIndex);

      return new Response(
        JSON.stringify(createSuccessResponse({
          results: paginatedResults,
          pagination: {
            page,
            limit,
            total: results.length,
            hasMore: endIndex < results.length,
          },
        })),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("获取监控结果失败:", error);
      return new Response(
        JSON.stringify(createErrorResponse("获取监控结果失败")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
