import { Handlers } from "$fresh/server.ts";
import { State } from "../../../_middleware.ts";
import { TaskService } from "../../../../lib/kv.ts";
import { createSuccessResponse, createErrorResponse } from "../../../../lib/utils.ts";
import { MonitorService } from "../../../../lib/monitor.ts";

export const handler: Handlers<unknown, State> = {
  // 手动执行任务
  async POST(req, ctx) {
    if (!ctx.state.user) {
      return new Response(
        JSON.stringify(createErrorResponse("未授权访问")),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    const url = new URL(req.url);
    const pathParts = url.pathname.split("/");
    const taskId = pathParts[pathParts.length - 2]; // 倒数第二个部分是 taskId

    if (!taskId) {
      return new Response(
        JSON.stringify(createErrorResponse("任务ID不能为空")),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      const task = await TaskService.getById(taskId);
      
      if (!task) {
        return new Response(
          JSON.stringify(createErrorResponse("任务不存在")),
          { status: 404, headers: { "Content-Type": "application/json" } }
        );
      }

      // 检查任务所有权
      if (task.userId !== ctx.state.user.id) {
        return new Response(
          JSON.stringify(createErrorResponse("无权执行此任务")),
          { status: 403, headers: { "Content-Type": "application/json" } }
        );
      }

      // 执行任务
      const result = await MonitorService.executeTask(task);

      // 更新任务的下次执行时间
      await MonitorService.updateTaskNextRun(taskId);

      return new Response(
        JSON.stringify(createSuccessResponse(result, "任务执行完成")),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("执行任务失败:", error);
      return new Response(
        JSON.stringify(createErrorResponse("执行任务失败")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
