import { Head } from "$fresh/runtime.ts";
import { Handlers, PageProps } from "$fresh/server.ts";
import { State } from "./_middleware.ts";
import { UserService } from "../lib/kv.ts";
import { 
  hashPassword, 
  generateId, 
  isValidEmail, 
  isValidUsername, 
  isValidPassword 
} from "../lib/auth.ts";
import { User } from "../types/index.ts";

interface RegisterData {
  error?: string;
  success?: string;
}

export const handler: Handlers<RegisterData, State> = {
  GET(req, ctx) {
    // 如果已登录，重定向到仪表板
    if (ctx.state.user) {
      return new Response("", {
        status: 302,
        headers: { Location: "/dashboard" },
      });
    }
    
    return ctx.render({});
  },

  async POST(req, ctx) {
    // 如果已登录，重定向到仪表板
    if (ctx.state.user) {
      return new Response("", {
        status: 302,
        headers: { Location: "/dashboard" },
      });
    }

    const form = await req.formData();
    const username = form.get("username")?.toString();
    const email = form.get("email")?.toString();
    const password = form.get("password")?.toString();
    const confirmPassword = form.get("confirmPassword")?.toString();

    // 验证输入
    if (!username || !email || !password || !confirmPassword) {
      return ctx.render({ error: "请填写所有必填字段" });
    }

    if (!isValidUsername(username)) {
      return ctx.render({ 
        error: "用户名格式不正确（3-20个字符，只能包含字母、数字、下划线）" 
      });
    }

    if (!isValidEmail(email)) {
      return ctx.render({ error: "邮箱格式不正确" });
    }

    if (!isValidPassword(password)) {
      return ctx.render({ 
        error: "密码格式不正确（至少8个字符，包含字母和数字）" 
      });
    }

    if (password !== confirmPassword) {
      return ctx.render({ error: "两次输入的密码不一致" });
    }

    try {
      // 检查用户名是否已存在
      const existingUser = await UserService.getByUsername(username);
      if (existingUser) {
        return ctx.render({ error: "用户名已存在" });
      }

      // 创建新用户
      const passwordHash = await hashPassword(password);
      const user: User = {
        id: generateId(),
        username,
        email,
        passwordHash,
        createdAt: new Date(),
      };

      await UserService.create(user);

      return ctx.render({ 
        success: "注册成功！请登录您的账号。" 
      });
    } catch (error) {
      console.error("注册错误:", error);
      return ctx.render({ error: "注册失败，请稍后重试" });
    }
  },
};

export default function Register({ data }: PageProps<RegisterData>) {
  return (
    <>
      <Head>
        <title>注册 - 网站监控系统</title>
      </Head>
      
      <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
          <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-900">创建账号</h2>
            <p class="mt-2 text-sm text-gray-600">
              已有账号？{" "}
              <a href="/login" class="font-medium text-blue-600 hover:text-blue-500">
                立即登录
              </a>
            </p>
          </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <form class="space-y-6" method="POST">
              {data?.error && (
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                  {data.error}
                </div>
              )}

              {data?.success && (
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                  {data.success}
                  <div class="mt-2">
                    <a href="/login" class="font-medium text-green-600 hover:text-green-500">
                      点击这里登录 →
                    </a>
                  </div>
                </div>
              )}

              <div>
                <label htmlFor="username" class="block text-sm font-medium text-gray-700">
                  用户名
                </label>
                <div class="mt-1">
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="3-20个字符，字母数字下划线"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" class="block text-sm font-medium text-gray-700">
                  邮箱地址
                </label>
                <div class="mt-1">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="请输入邮箱地址"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" class="block text-sm font-medium text-gray-700">
                  密码
                </label>
                <div class="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="至少8个字符，包含字母和数字"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" class="block text-sm font-medium text-gray-700">
                  确认密码
                </label>
                <div class="mt-1">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="请再次输入密码"
                  />
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  注册
                </button>
              </div>
            </form>

            <div class="mt-6">
              <div class="relative">
                <div class="absolute inset-0 flex items-center">
                  <div class="w-full border-t border-gray-300" />
                </div>
                <div class="relative flex justify-center text-sm">
                  <span class="px-2 bg-white text-gray-500">或者</span>
                </div>
              </div>

              <div class="mt-6">
                <a
                  href="/"
                  class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors"
                >
                  返回首页
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
