import { MiddlewareHandlerContext } from "$fresh/server.ts";
import { getUserFromCookie } from "../lib/auth.ts";

export interface State {
  user?: {
    id: string;
    username: string;
    email: string;
  };
}

export async function handler(
  req: Request,
  ctx: MiddlewareHandlerContext<State>,
) {
  // 尝试从 Cookie 获取用户信息
  const user = await getUserFromCookie(req);
  
  if (user) {
    ctx.state.user = {
      id: user.id,
      username: user.username,
      email: user.email,
    };
  }

  // 检查是否需要认证的路由
  const url = new URL(req.url);
  const protectedPaths = ["/dashboard", "/api/tasks", "/api/results"];
  
  const isProtectedPath = protectedPaths.some(path => 
    url.pathname.startsWith(path)
  );

  // 如果是受保护的路由且用户未登录，重定向到登录页
  if (isProtectedPath && !user) {
    if (url.pathname.startsWith("/api/")) {
      // API 路由返回 401
      return new Response(
        JSON.stringify({ success: false, error: "未授权访问" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    } else {
      // 页面路由重定向到登录页
      return new Response("", {
        status: 302,
        headers: { Location: "/login" },
      });
    }
  }

  return ctx.next();
}
