import { Head } from "$fresh/runtime.ts";
import { Handlers, PageProps } from "$fresh/server.ts";
import { State } from "./_middleware.ts";
import { UserService } from "../lib/kv.ts";
import { verifyPassword, generateJWT, createAuthCookie } from "../lib/auth.ts";

interface LoginData {
  error?: string;
  success?: string;
}

export const handler: Handlers<LoginData, State> = {
  GET(req, ctx) {
    // 如果已登录，重定向到仪表板
    if (ctx.state.user) {
      return new Response("", {
        status: 302,
        headers: { Location: "/dashboard" },
      });
    }
    
    return ctx.render({});
  },

  async POST(req, ctx) {
    // 如果已登录，重定向到仪表板
    if (ctx.state.user) {
      return new Response("", {
        status: 302,
        headers: { Location: "/dashboard" },
      });
    }

    const form = await req.formData();
    const username = form.get("username")?.toString();
    const password = form.get("password")?.toString();

    if (!username || !password) {
      return ctx.render({ error: "请填写用户名和密码" });
    }

    try {
      // 查找用户
      const user = await UserService.getByUsername(username);
      if (!user) {
        return ctx.render({ error: "用户名或密码错误" });
      }

      // 验证密码
      const isValidPassword = await verifyPassword(password, user.passwordHash);
      if (!isValidPassword) {
        return ctx.render({ error: "用户名或密码错误" });
      }

      // 更新最后登录时间
      await UserService.update(user.id, { lastLoginAt: new Date() });

      // 生成 JWT
      const token = await generateJWT(user);

      // 设置 Cookie 并重定向
      return new Response("", {
        status: 302,
        headers: {
          Location: "/dashboard",
          "Set-Cookie": createAuthCookie(token),
        },
      });
    } catch (error) {
      console.error("登录错误:", error);
      return ctx.render({ error: "登录失败，请稍后重试" });
    }
  },
};

export default function Login({ data }: PageProps<LoginData>) {
  return (
    <>
      <Head>
        <title>登录 - 网站监控系统</title>
      </Head>
      
      <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
          <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-900">登录账号</h2>
            <p class="mt-2 text-sm text-gray-600">
              还没有账号？{" "}
              <a href="/register" class="font-medium text-blue-600 hover:text-blue-500">
                立即注册
              </a>
            </p>
          </div>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <form class="space-y-6" method="POST">
              {data?.error && (
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                  {data.error}
                </div>
              )}

              {data?.success && (
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                  {data.success}
                </div>
              )}

              <div>
                <label htmlFor="username" class="block text-sm font-medium text-gray-700">
                  用户名
                </label>
                <div class="mt-1">
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="请输入用户名"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" class="block text-sm font-medium text-gray-700">
                  密码
                </label>
                <div class="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="请输入密码"
                  />
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  登录
                </button>
              </div>
            </form>

            <div class="mt-6">
              <div class="relative">
                <div class="absolute inset-0 flex items-center">
                  <div class="w-full border-t border-gray-300" />
                </div>
                <div class="relative flex justify-center text-sm">
                  <span class="px-2 bg-white text-gray-500">或者</span>
                </div>
              </div>

              <div class="mt-6">
                <a
                  href="/"
                  class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors"
                >
                  返回首页
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
