import { Head } from "$fresh/runtime.ts";
import { PageProps } from "$fresh/server.ts";
import { State } from "./_middleware.ts";

export default function Home(props: PageProps<unknown, State>) {
  const { user } = props.state;

  return (
    <>
      <Head>
        <title>网站监控系统</title>
        <meta name="description" content="定时访问网站并测速的监控系统" />
      </Head>
      
      <div class="min-h-screen bg-gray-50">
        {/* 导航栏 */}
        <nav class="bg-white shadow-sm border-b">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex items-center">
                <h1 class="text-xl font-semibold text-gray-900">
                  网站监控系统
                </h1>
              </div>
              <div class="flex items-center space-x-4">
                {user ? (
                  <>
                    <span class="text-gray-700">欢迎，{user.username}</span>
                    <a
                      href="/dashboard"
                      class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                      控制台
                    </a>
                    <a
                      href="/api/auth/logout"
                      class="text-gray-600 hover:text-gray-900 transition-colors"
                    >
                      退出
                    </a>
                  </>
                ) : (
                  <>
                    <a
                      href="/login"
                      class="text-gray-600 hover:text-gray-900 transition-colors"
                    >
                      登录
                    </a>
                    <a
                      href="/register"
                      class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                      注册
                    </a>
                  </>
                )}
              </div>
            </div>
          </div>
        </nav>

        {/* 主要内容 */}
        <main class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div class="text-center">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">
              网站监控与测速系统
            </h2>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              定时访问指定网站，监控网站状态和响应时间，支持自定义请求头、Cookie 和随机间隔时间。
              提供完整的日志记录和数据分析功能。
            </p>

            {/* 功能特性 */}
            <div class="grid md:grid-cols-3 gap-8 mt-12">
              <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">定时监控</h3>
                <p class="text-gray-600">
                  支持自定义监控间隔和随机范围，确保监控的灵活性和真实性
                </p>
              </div>

              <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">性能分析</h3>
                <p class="text-gray-600">
                  实时记录响应时间、状态码和成功率，提供详细的性能分析报告
                </p>
              </div>

              <div class="bg-white p-6 rounded-lg shadow-sm border">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">安全可靠</h3>
                <p class="text-gray-600">
                  支持用户认证、数据加密存储，确保监控数据的安全性和隐私性
                </p>
              </div>
            </div>

            {/* 行动按钮 */}
            <div class="mt-12">
              {user ? (
                <a
                  href="/dashboard"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  进入控制台
                  <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                </a>
              ) : (
                <div class="space-x-4">
                  <a
                    href="/register"
                    class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    立即开始
                  </a>
                  <a
                    href="/login"
                    class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    已有账号？登录
                  </a>
                </div>
              )}
            </div>
          </div>
        </main>

        {/* 页脚 */}
        <footer class="bg-white border-t mt-20">
          <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500">
              <p>&copy; 2025 网站监控系统. 基于 Deno Deploy 构建.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
