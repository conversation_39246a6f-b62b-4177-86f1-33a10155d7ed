import { KV_KEYS, MonitorResult, MonitorTask, SystemLog, User } from "../types/index.ts";

// KV 数据库实例
let kv: Deno.Kv;

// 初始化 KV 数据库
export async function initKV(): Promise<void> {
  try {
    // 检查是否支持 KV
    if (typeof Deno.openKv === 'function') {
      kv = await Deno.openKv();
      console.log("✅ KV 数据库初始化成功");
    } else {
      // 在开发环境中使用内存模拟
      console.log("⚠️ KV 不可用，使用内存模拟");
      kv = createMockKV();
    }
  } catch (error) {
    console.error("❌ KV 数据库初始化失败:", error);
    // 在失败时也使用内存模拟
    console.log("⚠️ 使用内存模拟替代");
    kv = createMockKV();
  }
}

// 创建模拟 KV 实例
function createMockKV(): Deno.Kv {
  const storage = new Map<string, any>();

  return {
    async get(key: Deno.KvKey) {
      const keyStr = JSON.stringify(key);
      const value = storage.get(keyStr);
      return { value: value || null, versionstamp: null };
    },

    async set(key: Deno.KvKey, value: any) {
      const keyStr = JSON.stringify(key);
      storage.set(keyStr, value);
      return { ok: true, versionstamp: "mock" };
    },

    async delete(key: Deno.KvKey) {
      const keyStr = JSON.stringify(key);
      storage.delete(keyStr);
    },

    list(selector: any) {
      const prefix = JSON.stringify(selector.prefix || []);
      const results: any[] = [];

      for (const [keyStr, value] of storage.entries()) {
        if (keyStr.startsWith(prefix.slice(0, -1))) { // 移除最后的 ]
          const key = JSON.parse(keyStr);
          results.push({ key, value, versionstamp: "mock" });
        }
      }

      return {
        async *[Symbol.asyncIterator]() {
          for (const result of results) {
            yield result;
          }
        }
      };
    },

    close() {
      // Mock close
    }
  } as any;
}

// 获取 KV 实例
export function getKV(): Deno.Kv {
  if (!kv) {
    throw new Error("KV 数据库未初始化");
  }
  return kv;
}

// 用户相关操作
export class UserService {
  static async create(user: User): Promise<void> {
    const kvInstance = getKV();
    await kvInstance.set([KV_KEYS.USER, user.id], user);
  }

  static async getById(id: string): Promise<User | null> {
    const kvInstance = getKV();
    const result = await kvInstance.get<User>([KV_KEYS.USER, id]);
    return result.value;
  }

  static async getByUsername(username: string): Promise<User | null> {
    const kvInstance = getKV();
    const iter = kvInstance.list<User>({ prefix: [KV_KEYS.USER] });
    for await (const entry of iter) {
      if (entry.value.username === username) {
        return entry.value;
      }
    }
    return null;
  }

  static async update(id: string, updates: Partial<User>): Promise<void> {
    const kvInstance = getKV();
    const existing = await this.getById(id);
    if (!existing) {
      throw new Error("用户不存在");
    }
    const updated = { ...existing, ...updates };
    await kvInstance.set([KV_KEYS.USER, id], updated);
  }

  static async delete(id: string): Promise<void> {
    const kvInstance = getKV();
    await kvInstance.delete([KV_KEYS.USER, id]);
  }
}

// 任务相关操作
export class TaskService {
  static async create(task: MonitorTask): Promise<void> {
    const kvInstance = getKV();
    await kvInstance.set([KV_KEYS.TASK, task.id], task);
    
    // 添加到用户任务列表
    const userTasks = await this.getUserTasks(task.userId);
    userTasks.push(task.id);
    await kvInstance.set([KV_KEYS.USER_TASKS, task.userId], userTasks);
  }

  static async getById(id: string): Promise<MonitorTask | null> {
    const kvInstance = getKV();
    const result = await kvInstance.get<MonitorTask>([KV_KEYS.TASK, id]);
    return result.value;
  }

  static async getUserTasks(userId: string): Promise<string[]> {
    const kvInstance = getKV();
    const result = await kvInstance.get<string[]>([KV_KEYS.USER_TASKS, userId]);
    return result.value || [];
  }

  static async getTasksByUser(userId: string): Promise<MonitorTask[]> {
    const taskIds = await this.getUserTasks(userId);
    const tasks: MonitorTask[] = [];
    
    for (const taskId of taskIds) {
      const task = await this.getById(taskId);
      if (task) {
        tasks.push(task);
      }
    }
    
    return tasks;
  }

  static async getAllActiveTasks(): Promise<MonitorTask[]> {
    const kvInstance = getKV();
    const tasks: MonitorTask[] = [];
    const iter = kvInstance.list<MonitorTask>({ prefix: [KV_KEYS.TASK] });
    
    for await (const entry of iter) {
      if (entry.value.isActive) {
        tasks.push(entry.value);
      }
    }
    
    return tasks;
  }

  static async update(id: string, updates: Partial<MonitorTask>): Promise<void> {
    const kvInstance = getKV();
    const existing = await this.getById(id);
    if (!existing) {
      throw new Error("任务不存在");
    }
    const updated = { ...existing, ...updates, updatedAt: new Date() };
    await kvInstance.set([KV_KEYS.TASK, id], updated);
  }

  static async delete(id: string): Promise<void> {
    const kvInstance = getKV();
    const task = await this.getById(id);
    if (!task) return;

    // 从用户任务列表中移除
    const userTasks = await this.getUserTasks(task.userId);
    const filteredTasks = userTasks.filter(taskId => taskId !== id);
    await kvInstance.set([KV_KEYS.USER_TASKS, task.userId], filteredTasks);

    // 删除任务
    await kvInstance.delete([KV_KEYS.TASK, id]);
  }
}

// 监控结果相关操作
export class ResultService {
  static async create(result: MonitorResult): Promise<void> {
    const kvInstance = getKV();
    await kvInstance.set([KV_KEYS.RESULT, result.id], result);
    
    // 添加到任务结果列表
    const taskResults = await this.getTaskResults(result.taskId);
    taskResults.push(result.id);
    await kvInstance.set([KV_KEYS.TASK_RESULTS, result.taskId], taskResults);
  }

  static async getTaskResults(taskId: string): Promise<string[]> {
    const kvInstance = getKV();
    const result = await kvInstance.get<string[]>([KV_KEYS.TASK_RESULTS, taskId]);
    return result.value || [];
  }

  static async getResultsByTask(taskId: string, limit = 100): Promise<MonitorResult[]> {
    const resultIds = await this.getTaskResults(taskId);
    const results: MonitorResult[] = [];
    
    // 获取最新的结果
    const recentIds = resultIds.slice(-limit);
    
    for (const resultId of recentIds) {
      const kvInstance = getKV();
      const result = await kvInstance.get<MonitorResult>([KV_KEYS.RESULT, resultId]);
      if (result.value) {
        results.push(result.value);
      }
    }
    
    return results.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  static async getRecentResults(limit = 50): Promise<MonitorResult[]> {
    const kvInstance = getKV();
    const results: MonitorResult[] = [];
    const iter = kvInstance.list<MonitorResult>({ prefix: [KV_KEYS.RESULT] });
    
    for await (const entry of iter) {
      results.push(entry.value);
    }
    
    return results
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }
}

// 日志相关操作
export class LogService {
  static async create(log: SystemLog): Promise<void> {
    const kvInstance = getKV();
    await kvInstance.set([KV_KEYS.LOG, log.id], log);
  }

  static async getLogs(limit = 100): Promise<SystemLog[]> {
    const kvInstance = getKV();
    const logs: SystemLog[] = [];
    const iter = kvInstance.list<SystemLog>({ prefix: [KV_KEYS.LOG] });

    for await (const entry of iter) {
      logs.push(entry.value);
    }

    return logs
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  static async cleanupOldLogs(retentionDays: number): Promise<number> {
    const kvInstance = getKV();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    let deletedCount = 0;
    const iter = kvInstance.list<SystemLog>({ prefix: [KV_KEYS.LOG] });

    for await (const entry of iter) {
      if (entry.value.timestamp < cutoffDate) {
        await kvInstance.delete(entry.key);
        deletedCount++;
      }
    }

    return deletedCount;
  }
}

// 配置相关操作
export class ConfigService {
  static async getConfig(): Promise<any> {
    const kvInstance = getKV();
    const result = await kvInstance.get([KV_KEYS.CONFIG]);
    return result.value || {
      logRetentionDays: 30,
      maxTasksPerUser: 10,
      minIntervalMinutes: 1,
      maxIntervalMinutes: 1440,
      enableRegistration: true,
    };
  }

  static async updateConfig(config: any): Promise<void> {
    const kvInstance = getKV();
    await kvInstance.set([KV_KEYS.CONFIG], config);
  }
}
