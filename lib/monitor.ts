import { MonitorResult, MonitorTask, SystemLog } from "../types/index.ts";
import { generateId } from "./auth.ts";
import { LogService, ResultService, TaskService } from "./kv.ts";

// 监控服务
export class MonitorService {
  private static runningTasks = new Set<string>();

  // 执行单个监控任务
  static async executeTask(task: MonitorTask): Promise<MonitorResult> {
    const startTime = Date.now();
    const resultId = generateId();
    
    try {
      // 记录开始执行
      await LogService.create({
        id: generateId(),
        level: "INFO",
        message: `开始执行监控任务: ${task.name}`,
        taskId: task.id,
        timestamp: new Date(),
      });

      // 准备请求头
      const headers = new Headers();
      
      // 设置默认请求头
      headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
      headers.set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8");
      headers.set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
      headers.set("Accept-Encoding", "gzip, deflate, br");
      headers.set("Cache-Control", "no-cache");
      headers.set("Pragma", "no-cache");

      // 添加自定义请求头
      if (task.headers) {
        for (const [key, value] of Object.entries(task.headers)) {
          headers.set(key, value);
        }
      }

      // 添加 Cookie
      if (task.useCookie && task.cookies) {
        headers.set("Cookie", task.cookies);
      }

      // 准备请求选项
      const requestOptions: RequestInit = {
        method: task.method,
        headers,
        redirect: "follow",
      };

      // 添加请求体（仅对 POST 请求）
      if (task.method === "POST" && task.body) {
        requestOptions.body = task.body;
        if (!headers.has("Content-Type")) {
          headers.set("Content-Type", "application/x-www-form-urlencoded");
        }
      }

      // 执行请求
      const response = await fetch(task.url, requestOptions);
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // 读取响应内容以获取大小
      const responseText = await response.text();
      const responseSize = new TextEncoder().encode(responseText).length;

      // 创建监控结果
      const result: MonitorResult = {
        id: resultId,
        taskId: task.id,
        url: task.url,
        status: response.status,
        responseTime,
        success: response.ok,
        responseSize,
        timestamp: new Date(),
      };

      // 如果请求失败，记录错误信息
      if (!response.ok) {
        result.error = `HTTP ${response.status}: ${response.statusText}`;
      }

      // 保存结果
      await ResultService.create(result);

      // 记录执行完成
      await LogService.create({
        id: generateId(),
        level: result.success ? "INFO" : "WARN",
        message: `监控任务执行完成: ${task.name} - 状态: ${result.status}, 响应时间: ${result.responseTime}ms`,
        details: {
          taskId: task.id,
          status: result.status,
          responseTime: result.responseTime,
          success: result.success,
        },
        taskId: task.id,
        timestamp: new Date(),
      });

      return result;

    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // 创建错误结果
      const result: MonitorResult = {
        id: resultId,
        taskId: task.id,
        url: task.url,
        status: 0,
        responseTime,
        success: false,
        error: error.message,
        timestamp: new Date(),
      };

      // 保存错误结果
      await ResultService.create(result);

      // 记录错误
      await LogService.create({
        id: generateId(),
        level: "ERROR",
        message: `监控任务执行失败: ${task.name}`,
        details: {
          taskId: task.id,
          error: error.message,
          responseTime,
        },
        taskId: task.id,
        timestamp: new Date(),
      });

      return result;
    }
  }

  // 计算下次执行时间
  static calculateNextRunTime(task: MonitorTask): Date {
    const now = new Date();
    const baseInterval = task.intervalMinutes * 60 * 1000; // 转换为毫秒
    
    // 添加随机范围
    let randomOffset = 0;
    if (task.randomRangeMinutes > 0) {
      randomOffset = Math.random() * task.randomRangeMinutes * 60 * 1000;
    }
    
    return new Date(now.getTime() + baseInterval + randomOffset);
  }

  // 更新任务的下次执行时间
  static async updateTaskNextRun(taskId: string): Promise<void> {
    const task = await TaskService.getById(taskId);
    if (!task) return;

    const nextRunAt = this.calculateNextRunTime(task);
    await TaskService.update(taskId, {
      lastRunAt: new Date(),
      nextRunAt,
    });
  }

  // 检查是否应该执行任务
  static shouldExecuteTask(task: MonitorTask): boolean {
    if (!task.isActive) return false;
    if (!task.nextRunAt) return true; // 首次执行
    
    return new Date() >= task.nextRunAt;
  }

  // 执行所有到期的任务
  static async executeScheduledTasks(): Promise<void> {
    try {
      const activeTasks = await TaskService.getAllActiveTasks();
      
      for (const task of activeTasks) {
        // 避免重复执行同一个任务
        if (this.runningTasks.has(task.id)) {
          continue;
        }

        if (this.shouldExecuteTask(task)) {
          // 标记任务正在执行
          this.runningTasks.add(task.id);
          
          try {
            // 异步执行任务
            this.executeTask(task).then(async () => {
              // 更新下次执行时间
              await this.updateTaskNextRun(task.id);
              // 移除执行标记
              this.runningTasks.delete(task.id);
            }).catch(async (error) => {
              // 记录错误
              await LogService.create({
                id: generateId(),
                level: "ERROR",
                message: `任务执行异常: ${task.name}`,
                details: { error: error.message },
                taskId: task.id,
                timestamp: new Date(),
              });
              // 移除执行标记
              this.runningTasks.delete(task.id);
            });
          } catch (error) {
            // 移除执行标记
            this.runningTasks.delete(task.id);
            throw error;
          }
        }
      }
    } catch (error) {
      await LogService.create({
        id: generateId(),
        level: "ERROR",
        message: "执行定时任务时发生错误",
        details: { error: error.message },
        timestamp: new Date(),
      });
    }
  }

  // 获取任务统计信息
  static async getTaskStats(taskId: string): Promise<{
    totalRuns: number;
    successRate: number;
    averageResponseTime: number;
    lastResult?: MonitorResult;
  }> {
    const results = await ResultService.getResultsByTask(taskId, 100);
    
    if (results.length === 0) {
      return {
        totalRuns: 0,
        successRate: 0,
        averageResponseTime: 0,
      };
    }

    const successCount = results.filter(r => r.success).length;
    const totalResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0);

    return {
      totalRuns: results.length,
      successRate: (successCount / results.length) * 100,
      averageResponseTime: totalResponseTime / results.length,
      lastResult: results[0],
    };
  }
}
