import { MonitorService } from "./monitor.ts";
import { LogService, ConfigService } from "./kv.ts";
import { generateId } from "./auth.ts";

// 定时任务调度器
export class Scheduler {
  private static isInitialized = false;

  // 初始化调度器
  static async init(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 记录调度器启动
      await LogService.create({
        id: generateId(),
        level: "INFO",
        message: "监控调度器启动",
        timestamp: new Date(),
      });

      // 设置定时任务 - 每分钟检查一次
      Deno.cron("Monitor Tasks", "* * * * *", async () => {
        await this.executeScheduledTasks();
      });

      // 设置日志清理任务 - 每天凌晨2点执行
      Deno.cron("Cleanup Logs", "0 2 * * *", async () => {
        await this.cleanupOldLogs();
      });

      this.isInitialized = true;

      await LogService.create({
        id: generateId(),
        level: "INFO",
        message: "监控调度器初始化完成",
        timestamp: new Date(),
      });

      console.log("监控调度器已启动");
    } catch (error) {
      console.error("调度器初始化失败:", error);
      
      await LogService.create({
        id: generateId(),
        level: "ERROR",
        message: "监控调度器初始化失败",
        details: { error: error.message },
        timestamp: new Date(),
      });
    }
  }

  // 执行定时任务
  private static async executeScheduledTasks(): Promise<void> {
    try {
      await MonitorService.executeScheduledTasks();
    } catch (error) {
      console.error("执行定时任务失败:", error);
      
      await LogService.create({
        id: generateId(),
        level: "ERROR",
        message: "执行定时任务失败",
        details: { error: error.message },
        timestamp: new Date(),
      });
    }
  }

  // 清理旧日志
  private static async cleanupOldLogs(): Promise<void> {
    try {
      const config = await ConfigService.getConfig();
      const retentionDays = config.logRetentionDays || 30;
      
      const deletedCount = await LogService.cleanupOldLogs(retentionDays);
      
      await LogService.create({
        id: generateId(),
        level: "INFO",
        message: `日志清理完成，删除了 ${deletedCount} 条过期日志`,
        details: { 
          deletedCount, 
          retentionDays 
        },
        timestamp: new Date(),
      });

      console.log(`日志清理完成，删除了 ${deletedCount} 条过期日志`);
    } catch (error) {
      console.error("清理日志失败:", error);
      
      await LogService.create({
        id: generateId(),
        level: "ERROR",
        message: "清理日志失败",
        details: { error: error.message },
        timestamp: new Date(),
      });
    }
  }

  // 获取调度器状态
  static getStatus(): { isInitialized: boolean } {
    return {
      isInitialized: this.isInitialized,
    };
  }
}
