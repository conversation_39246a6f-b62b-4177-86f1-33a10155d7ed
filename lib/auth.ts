import * as bcrypt from "bcrypt";
import { SignJWT, jwtVerify } from "jose";
import { JWTPayload, User } from "../types/index.ts";
import { UserService } from "./kv.ts";

// JWT 密钥
const JWT_SECRET = new TextEncoder().encode(
  Deno.env.get("JWT_SECRET") || "your-super-secret-jwt-key-change-in-production"
);

// JWT 过期时间（7天）
const JWT_EXPIRES_IN = 7 * 24 * 60 * 60; // 秒

// 密码哈希
export async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password);
}

// 验证密码
export async function verifyPassword(password: string, hashValue: string): Promise<boolean> {
  try {
    return await bcrypt.verify(password, hashValue);
  } catch {
    return false;
  }
}

// 生成 JWT
export async function generateJWT(user: User): Promise<string> {
  const payload: JWTPayload = {
    userId: user.id,
    username: user.username,
    exp: Math.floor(Date.now() / 1000) + JWT_EXPIRES_IN,
  };

  return await new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime(payload.exp)
    .sign(JWT_SECRET);
}

// 验证 JWT
export async function verifyJWT(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload as JWTPayload;
  } catch {
    return null;
  }
}

// 从请求中获取用户
export async function getUserFromRequest(request: Request): Promise<User | null> {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = await verifyJWT(token);
  if (!payload) {
    return null;
  }

  return await UserService.getById(payload.userId);
}

// 从 Cookie 中获取用户
export async function getUserFromCookie(request: Request): Promise<User | null> {
  const cookieHeader = request.headers.get("Cookie");
  if (!cookieHeader) {
    return null;
  }

  const cookies = parseCookies(cookieHeader);
  const token = cookies.auth_token;
  if (!token) {
    return null;
  }

  const payload = await verifyJWT(token);
  if (!payload) {
    return null;
  }

  return await UserService.getById(payload.userId);
}

// 解析 Cookie
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};
  
  cookieHeader.split(";").forEach(cookie => {
    const [name, value] = cookie.trim().split("=");
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });

  return cookies;
}

// 生成随机 ID
export function generateId(): string {
  return crypto.randomUUID();
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证用户名格式
export function isValidUsername(username: string): boolean {
  // 用户名：3-20个字符，只能包含字母、数字、下划线
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
}

// 验证密码强度
export function isValidPassword(password: string): boolean {
  // 密码：至少8个字符，包含字母和数字
  return password.length >= 8 && /[a-zA-Z]/.test(password) && /[0-9]/.test(password);
}

// 创建认证 Cookie
export function createAuthCookie(token: string): string {
  const maxAge = JWT_EXPIRES_IN;
  return `auth_token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=${maxAge}; Path=/`;
}

// 清除认证 Cookie
export function clearAuthCookie(): string {
  return "auth_token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/";
}
