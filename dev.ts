import { start } from "$fresh/server.ts";
import dev from "$fresh/dev.ts";
import config from "./fresh.config.ts";
import manifest from "./fresh.gen.ts";
import { initKV } from "./lib/kv.ts";
import { Scheduler } from "./lib/scheduler.ts";

// 检测环境
const isDevelopment = Deno.env.get("DENO_DEPLOYMENT_ID") === undefined;

// 在开发环境中手动设置环境变量
if (isDevelopment) {
  Deno.env.set("JWT_SECRET", "your-super-secret-jwt-key-change-in-production-12345678");
  Deno.env.set("DENO_ENV", "development");
}

// 初始化应用
async function initApp() {
  try {
    // 初始化 KV 数据库
    await initKV();
    console.log("✅ KV 数据库初始化完成");

    // 初始化调度器
    await Scheduler.init();
    console.log("✅ 监控调度器初始化完成");

    console.log("🚀 应用初始化完成");
  } catch (error) {
    console.error("❌ 应用初始化失败:", error);
    if (!isDevelopment) {
      Deno.exit(1);
    }
  }
}

// 启动应用
async function main() {
  await initApp();

  if (isDevelopment) {
    // 开发环境：使用 Fresh 开发服务器
    await dev(import.meta.url, "./main.ts", config);
  } else {
    // 生产环境：直接启动服务器
    await start(manifest, config);
  }
}

await main();
