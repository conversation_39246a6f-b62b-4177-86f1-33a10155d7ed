#!/usr/bin/env -S deno run -A --watch=static/,routes/

import dev from "$fresh/dev.ts";
import config from "./fresh.config.ts";
import { initKV } from "./lib/kv.ts";
import { Scheduler } from "./lib/scheduler.ts";

// 手动设置环境变量
Deno.env.set("JWT_SECRET", "your-super-secret-jwt-key-change-in-production-12345678");
Deno.env.set("DENO_ENV", "development");

// 初始化应用
async function initApp() {
  try {
    // 初始化 KV 数据库
    await initKV();
    console.log("✅ KV 数据库初始化完成");

    // 初始化调度器
    await Scheduler.init();
    console.log("✅ 监控调度器初始化完成");

    console.log("🚀 应用初始化完成");
  } catch (error) {
    console.error("❌ 应用初始化失败:", error);
    Deno.exit(1);
  }
}

// 先初始化，再启动开发服务器
await initApp();
await dev(import.meta.url, "./main.ts", config);
